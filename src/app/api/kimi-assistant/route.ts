import { NextRequest, NextResponse } from 'next/server'
import { GoogleGenAI, Type } from "@google/genai";
import { designSpecExample, htmlDesignExample } from '@/app/data/output_examples';
import OpenAI from "openai";

interface ResponseData {
  research: string;
  HTMLDesigns?: string;
}

interface TavilyResponse {
  results: Array<{
    url: string;
    title: string;
    content: string;
  }>;
}

const researcherModel = "moonshotai/kimi-k2-instruct"
const designerModel = "moonshotai/kimi-k2-instruct"

// Initialize OpenAI client for Groq with Kimi-K2
const client = new OpenAI({
  apiKey: process.env.GROQ_API_KEY || "********************************************************",
  baseURL: "https://api.groq.com/openai/v1"
});

// Tavily search function
async function searchTavily(query: string): Promise<TavilyResponse> {
  console.log('🔍 [Tavily] Initiating search with query:', query);
  
  try {
    const response = await fetch('https://api.tavily.com/search', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.TAVILY_TOKEN}`
      },
      body: JSON.stringify({
        query,
        search_depth: "advanced",
        max_results: 5
      })
    });

    if (!response.ok) {
      console.error('❌ [Tavily] API error:', {
        status: response.status,
        statusText: response.statusText
      });
      throw new Error(`Tavily API error: ${response.statusText}`);
    }

    const data = await response.json();
    console.log('✅ [Tavily] Search completed successfully with', data.results.length, 'results');
    
    // Only return the fields we need
    return {
      results: data.results.map((result: any) => ({
        url: result.url,
        title: result.title,
        content: result.content
      }))
    };
  } catch (error) {
    console.error('❌ [Tavily] Search failed:', error);
    throw error;
  }
}

// Tavily function declaration for OpenAI
const tavilySearchFunctionDeclaration = {
  type: "function" as const,
  function: {
    name: 'search_tavily',
    description: 'Searches the web for real-time information using Tavily API',
    parameters: {
      type: "object",
      properties: {
        query: {
          type: "string",
          description: 'The search query to find relevant information',
        }
      },
      required: ['query'],
    },
  }
};


export async function POST(request: NextRequest) {
  console.log('📝 [UX Assistant] Received new request');
  
  try {
    const { chatInput } = await request.json()
    console.log('🤖 [UX Assistant] Processing request:', chatInput);

    const message = chatInput;

    // Initialize researcher with function calling capability
    console.log('🔬 [Researcher] Starting research phase');
    const researcher_response = await client.chat.completions.create({
      model: researcherModel,
      messages: [
        {
          role: "system",
          content: "You are a helpful ux research assistant. You use the tool to get best practices for ux design implementations. Frame the question in a way that asks about the use of the patterns or approaches within ui designs. We are looking for best practices. The User's Design Request will be going to a designer, your job is to provide the research needed so that the designer can make the best design possible. You are providing research finding, not design direction. Your research is crucial to the success of the design, so use your intelligence to ask the right question. Make your question extremely concise and to the point as if you were entering it into a search engine. You ALWAYS use the tool. Don't use any quotes or special characters in your tool query. Your final response should be helpful research findings written using markdown."
        },
        {
          role: "user",
          content: "User's Design Request, for the designer: " + message
        }
      ],
      tools: [tavilySearchFunctionDeclaration],
      tool_choice: "auto"
    });

    let research = "";

    // Handle function calls if present (OpenAI format)
    const toolCalls = researcher_response.choices[0]?.message?.tool_calls;
    if (toolCalls && toolCalls.length > 0) {
      const toolCall = toolCalls[0];
      console.log('🔄 [Researcher] Function call detected:', toolCall.function.name);

      if (toolCall.function.name === 'search_tavily') {
        try {
          const args = JSON.parse(toolCall.function.arguments);
          if (args.query && typeof args.query === 'string') {
            console.log('🔍 [Researcher] Executing Tavily search with query:', args.query);
            // Execute the search
            const searchResult = await searchTavily(args.query);

            console.log('📚 [Researcher] Generating final research summary');
            // Get final response incorporating search results
            const finalResponse = await client.chat.completions.create({
              model: researcherModel,
              messages: [
                {
                  role: "system",
                  content: "You are a UX research assistant. Synthesize the search results into a comprehensive research summary."
                },
                {
                  role: "user",
                  content: "User's Design Request, for the designer: " + message
                },
                {
                  role: "assistant",
                  content: null,
                  tool_calls: [toolCall]
                },
                {
                  role: "tool",
                  content: JSON.stringify(searchResult),
                  tool_call_id: toolCall.id
                }
              ]
            });

            research = finalResponse.choices[0]?.message?.content || "";
            console.log('✅ [Researcher] Research phase completed successfully');
          }
        } catch (error) {
          console.error('❌ [Researcher] Error during research phase:', error);
          research = researcher_response.choices[0]?.message?.content || "";
        }
      }
    } else {
      console.log('ℹ️ [Researcher] No function calls detected, using direct response');
      research = researcher_response.choices[0]?.message?.content || "";
    }


    console.log('💻 [Designer] Starting HTML/CSS implementation phase');

const systemMessage = `You are an AI assistant specialized in translating design requests into clean, valid JSX code for a React application using  Shadcn UI components and standard HTML tags, styled with Tailwind CSS via the \`className\` prop. Your task is to generate a single JSX string based on the design request, ready to be parsed by \`react-jsx-parser\`.

**Input:** You will receive a design request. 

**Constraints:**
*   **Output Format:** Output **only** the final JSX string representing the entire specified UI (potentially multiple screen divs). Do **not** include import statements, explanations, comments, markdown formatting (like \\\`\\\`\\\`jsx), or any surrounding text.
*   **Valid JSX:** Ensure the output is syntactically correct JSX. Close all tags. Use correct JSX syntax for attributes (e.g., \`className\`, \`htmlFor\`, \`viewBox\`).
*   **Styling:**
    *   Use component props like \`variant\` and \`size\` when specified in the spec or appropriate.
    *   Apply Tailwind CSS utility classes **only** via the \`className\` prop.
    *   Do not use inline style objects (\`style={{}}\`).
    *   Do not use the \`primary\` or \`secondary\` for background or text or button colors.

*   **Screen Structure:** Each distinct mobile screen view described in the request should be enclosed in its own root \`<div>\` element with the \`mobilescreen\` class. Ensure these are sequential \`<div>\`s if multiple screens are generated.
*  **Icons:** Use lucide icons instead of svgs. A lucide icon would be declared like: <Lucide.Heart/> this declaration only applies to lucide icons. Shadcn components should be declared how they normally would be.  These are the lucide icons you have available to you: 

  **Basic UI & Navigation:**
  Home, Menu, X, Check, Plus, Minus, Search, Settings, Edit, Trash2, Save, Download, Upload, Share2, Printer, Copy, RefreshCw, Undo2, Redo2, Eye, EyeOff, Bell, Info, AlertTriangle, AlertCircle, Warning, HelpCircle, QuestionMarkCircle, Loader, Circle, Square, Triangle, Dribbble, Figma, Github, Twitter, Linkedin, Discord, Facebook, Instagram, Youtube, Google, Apple

  **Arrows & Directions:**
  ArrowUp, ArrowDown, ArrowLeft, ArrowRight, ChevronUp, ChevronDown, ChevronLeft, ChevronRight, ChevronsUp, ChevronsDown, ChevronsLeft, ChevronsRight, ArrowUpRight, ArrowDownRight, ArrowUpLeft, ArrowDownLeft, CornerUpRight, CornerDownRight, CornerUpLeft, CornerDownLeft, ExternalLink, Link, Move, GripHorizontal, GripVertical, Grip, ArrowUpDown, ArrowLeftRight

  **Files & Documents:**
  File, FileText, FileImage, FileVideo, FileAudio, FileSpreadsheet, FileArchive, Folder, FolderOpen, Document, Book, Paperclip, Clipboard, Terminal, Code, Database, Server, Cloud, CloudUpload, CloudDownload

  **Users & Accounts:**
  User, Users, UserPlus, UserMinus, UserCheck, UserX, UserRound, Lock, Unlock, Key, Shield, LogIn, LogOut, Profile, UserCog, Group, CircleUser

  **Media & Content:**
  Image, Video, Audio, Play, Pause, Stop, FastForward, Rewind, SkipForward, SkipBack, Volume, VolumeX, Camera, Mic, MicOff, Film, Monitor, Laptop, Tablet, Smartphone, Headphones, Speaker, Music, Clapperboard, GalleryThumbnails

  **Communication:**
  Mail, MessageSquare, MessageCircle, Phone, PhoneCall, PhoneMissed, PhoneOutgoing, PhoneIncoming, Send, AtSign, Rss

  **Data & Analytics:**
  ChartBar, ChartLine, PieChart, TrendingUp, TrendingDown, Table, List, Filter, SortAsc, SortDesc, SearchCheck, SearchX, Data, Grid3x3, Grid2x2, Kanban

  **Status & Feedback:**
  CheckCircle, XCircle, Info, HelpCircle, AlertCircle, AlertTriangle, Lightbulb, Star, Heart, Bookmark, Flag, ThumbsUp, ThumbsDown, Smile, Frown, Meh

  **Tools & Development:**
  Wrench, Hammer, Screwdriver, Brush, PenTool, Eraser, Scissors, Ruler, Layers, Palette, Crop, SlidersHorizontal, SlidersVertical, Target, Gauge, Crosshair, Bug, Command, Zap, Cpu, HardDrive, Ram

  **Time & Calendar:**
  Calendar, Clock, AlarmClock, History, Timer, Hourglass

  **Location & Maps:**
  MapPin, Map, Globe, Compass, Navigation, StreetMap

  **Shopping & E-commerce:**
  ShoppingCart, ShoppingBag, CreditCard, Wallet, Tag, Gift, Receipt, Package, Truck, Store, Percent, DollarSign, Euro, PoundSterling, Rupee, Bitcoin

  **Accessibility & Preferences:**
  Moon, Sun, Eye, EyeOff, Contrast, Accessibility, Syringe, Pipette, PaintBucket

  **Abstract & Concepts:**
  MagicWand, Sparkles, Brain, Bot, Robot, Gem, Crown, Puzzle, Infinity, Leaf, Anchor, CloudLightning, SunSnow, CloudRain, Wind, Droplet, Flame, Fire, Award, Trophy, Rocket, Satellite, SatelliteDish, Telescope, Microscope, Atom, Dna

  **Miscellaneous:**
  BellRing, BellOff, Map, MapPinOff, Wifi, WifiOff, Bluetooth, BluetoothOff, Usb, Power, Battery, BatteryCharging, Plug, CircleDot, MinusCircle, PlusCircle, Maximize, Minimize, Minimize2, Maximize2, Resize, ZoomIn, ZoomOut, Crop, RotateCw, RotateCcw, FlipHorizontal, FlipVertical, Blend, FilterX, Funnel, Hash, Asterisk, PlusSquare, MinusSquare, CheckSquare, XSquare

  Do not use any other Lucide icons that are not listed above.

* **Images:** For image urls use Picsum(example: <img src="https://picsum.photos/id/1/200/300" className=.../> ).
* **Shadcn Components:** These are the components you have available to you: Button, Input, Textarea, Label, Checkbox, Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle, Alert, AlertDescription, AlertTitle, Badge, Avatar, AvatarFallback, AvatarImage, Separator, Switch, Skeleton, Progress, Select, SelectContent, SelectItem, SelectTrigger, SelectValue, Tabs, TabsContent, TabsList, TabsTrigger, Toggle, Accordion, AccordionContent, AccordionItem, AccordionTrigger, Menubar, MenubarContent, MenubarItem, MenubarMenu, MenubarSeparator, MenubarTrigger, Calendar, Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList, RadioGroup, RadioGroupItem, Slider, Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator, Carousel, CarouselContent, CarouselItem, ScrollArea, ScrollBar, Table, TableBody, TableCaption, TableCell, TableFooter, TableHead, TableHeader, TableRow. Do not use any other Shadcn components that are not listed above.

---
**Important:** Generate JSX for **all** distinct screen sections (e.g., \`## Screen 1: ...\`, \`## Screen 2: ...\`) described in the request. Each screen **must** be a top-level \`<div>\` with the class \`mobilescreen\`, have a background color, and should contain all subsequent elements for that screen.  If you need to create overlays or modals, always avoid using position:fixed or position:absolute, this will mess things up, just show the overlays or modals as a new/separate screen/view/page. DO NOT USE "<section>, <main>, <header>, <footer>, <nav>" tags. DO NOT EVER USE "sticky" or "fixed" positioning. DO NOT USE ARRAYS OR MAPPING FUNCTIONS example: {data.map((item) => ( <div key={item.id}>{item.name}</div>))} this is very bad, never do this. Always apply an excellent content strategy to the design. The final output should look like production-ready, beautiful mobile screens when rendered, reflecting the high standards expected by a visual UX designer. We want user coming away with the feeling like they experienced something that exceeded expectations, not something that was quickly thrown together. Everyone is counting on you to produce superior and amazing work. You have decades of experience, you can do it!
---`

const systemMessage2 = `You are a UI component generator that creates beautiful, responsive web interfaces. Your output will be injected into an iframe with DaisyUI, TailwindCSS, and Lucide icons already loaded.

## CRITICAL RULES:
- ONLY provide the HTML content for the <body> tag (no <!DOCTYPE>, <html>, <head>, or <body> tags)
- All external dependencies (DaisyUI, TailwindCSS, Lucide icons) are already available
- Your output goes directly into: <body>${htmlCode}</body>

## STYLING REQUIREMENTS:
- Use DaisyUI components and classes exclusively (btn, card, navbar, hero, etc.)
- Make designs responsive from 450px to desktop using Tailwind breakpoints (sm:, md:, lg:)
- Use Lucide icons: <i data-lucide="icon-name" class="w-4 h-4"></i>
- For images, use Picsum: <img src="https://picsum.photos/id/123/400/300" class="..." />

## MULTI-PAGE HANDLING:
When a design needs multiple pages/sections, create a single-page application using:
- Multiple divs with unique IDs for each "page"
- JavaScript to show/hide pages: showPage('page1')
- Navigation that calls JavaScript functions
- Initially show only the first page

Example multi-page structure:
```html
<div class="min-h-screen">
  <!-- Navigation -->
  <div class="navbar bg-base-100">
    <button class="btn btn-ghost" onclick="showPage('home')">Home</button>
    <button class="btn btn-ghost" onclick="showPage('about')">About</button>
  </div>

  <!-- Page 1 -->
  <div id="home" class="page-content">
    <div class="hero min-h-screen"><!-- home content --></div>
  </div>

  <!-- Page 2 -->
  <div id="about" class="page-content hidden">
    <div class="container mx-auto p-8"><!-- about content --></div>
  </div>

  <script>
    function showPage(pageId) {
      document.querySelectorAll('.page-content').forEach(el => el.classList.add('hidden'));
      document.getElementById(pageId).classList.remove('hidden');
    }
    // Initialize Lucide icons
    lucide.createIcons();
  </script>
</div>`

const usermessageAndResearch = `The design request is: "Please design this: ${message}" and the research is ${research}`;
const conversationMessages = [
            { role: 'system' as const, content: systemMessage },{ role: 'user' as const, content: usermessageAndResearch },
        ];

    const groq_response = await client.chat.completions.create({
                model: designerModel,
                messages: conversationMessages,
                temperature: 0.1,
            });

    console.log('✅ [Designer] HTML/CSS implementation completed');
            console.log ('groq_response', groq_response.choices[0]?.message.content)

    const cleaned_designer_response = (groq_response.choices[0]?.message.content as string).replace(/```html/g, '').replace(/```/g, '').replace(/<img/gi, '<img draggable="false"').replace(/fixed/gi, '').replace(/absolute/gi, '').replace(/jsx/gi, '');



    const responseData: ResponseData = {
      research: research,
      HTMLDesigns: cleaned_designer_response,
    }

    console.log('✅ [UX Assistant] Request completed successfully');
    return NextResponse.json(responseData)
  } catch (error) {
    console.error('❌ [UX Assistant] Error processing request:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}